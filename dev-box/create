
#!/bin/bash -eu

gcloud compute instances create dev-box \
    --project=cardcomp \
    --zone=us-central1-c \
    --machine-type=e2-custom-4-16384 \
    --network-interface=network-tier=PREMIUM,stack-type=IPV4_ONLY,subnet=default \
    --metadata=enable-osconfig=TRUE \
    --maintenance-policy=MIGRATE \
    --provisioning-model=STANDARD \
    --service-account=<EMAIL> \
    --scopes=https://www.googleapis.com/auth/devstorage.read_write,https://www.googleapis.com/auth/logging.write,https://www.googleapis.com/auth/monitoring.write,https://www.googleapis.com/auth/service.management.readonly,https://www.googleapis.com/auth/servicecontrol,https://www.googleapis.com/auth/trace.append \
    --create-disk=auto-delete=yes,boot=yes,device-name=dev-box,disk-resource-policy=projects/cardcomp/regions/us-central1/resourcePolicies/default-schedule-1,image=projects/ubuntu-os-cloud/global/images/ubuntu-minimal-2410-oracular-amd64-v20250409,mode=rw,size=10,type=pd-balanced \
    --no-shielded-secure-boot \
    --shielded-vtpm \
    --shielded-integrity-monitoring \
    --labels=goog-ops-agent-policy=v2-x86-template-1-4-0,goog-ec-src=vm_add-gcloud \
    --reservation-affinity=any \
    --disk name=home-disk,device-name=home-disk,mode=rw,boot=no,auto-delete=no \
    --metadata-from-file="startup-script=$(dirname $0)/mount-and-bootstrap.sh" \
    

# gcloud compute instances ops-agents policies create goog-ops-agent-v2-x86-template-1-4-0-us-central1-c
#     --project=cardcomp \
#     --zone=us-central1-c \
#     --file=config.yaml

gcloud compute config-ssh
