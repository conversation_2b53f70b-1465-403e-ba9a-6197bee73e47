#!/bin/bash

DISK_ID="/dev/disk/by-id/scsi-0Google_PersistentDisk_home-disk"
MOUNT_POINT="/home"
FS_TYPE="ext4"

# Wait for the disk to appear (in case it's slow at boot)
for i in {1..10}; do
    if [ -e "$DISK_ID" ]; then
        break
    fi
    echo "Waiting for disk $DISK_ID..."
    sleep 2
done

# Create mount point if missing
mkdir -p "$MOUNT_POINT"

# Mount it if not already mounted
if ! mountpoint -q "$MOUNT_POINT"; then
    mount "$DISK_ID" "$MOUNT_POINT"
    echo "Mounted $DISK_ID at $MOUNT_POINT"
fi


# Make sure /etc/fstab has the correct entry
FSTAB_ENTRY="$DISK_ID $MOUNT_POINT $FS_TYPE defaults,nofail,discard 0 2"
grep -q "$DISK_ID" /etc/fstab || echo "$FSTAB_ENTRY" >> /etc/fstab


################ Run ~/bootstrap.sh ##############

sudo apt update
sudo apt install -y git

# /home/<USER>/bootstrap.sh