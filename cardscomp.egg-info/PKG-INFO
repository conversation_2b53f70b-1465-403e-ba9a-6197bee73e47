Metadata-Version: 2.4
Name: cardscomp
Version: 0.1.0
Summary: Basketball card data analysis tools
License: MIT
Requires-Python: >=3.12
Description-Content-Type: text/markdown

# cardscomp

```
sudo apt update && sudo apt install -y git
```

```
gcloud compute disks create home-disk \
    --size=10GB \
    --zone=us-central1-c \
    --type=pd-standard

gcloud compute instances attach-disk dev-box \
    --disk=home-disk \
    --zone=us-central1-c

```

## Development Setup

To properly set up your Python interpreter for VSCode Jupyter:

1. Install the package in development mode:

```bash
pip install -e .
```

2. Make sure VSCode is using the correct Python interpreter:
   - Press `Cmd+Shift+P` (Mac) or `Ctrl+Shift+P` (Windows/Linux)
   - Type "Python: Select Interpreter" and select the appropriate environment

3. For Jupyter notebooks, ensure the kernel is set to the correct Python environment:
   - Open a notebook
   - Click on the kernel selector in the top right
   - Select the kernel that matches your environment

This setup ensures that imports like `image_features.serialization` work correctly in <PERSON>pyter notebooks.
