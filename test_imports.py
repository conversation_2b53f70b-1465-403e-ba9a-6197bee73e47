"""
Test script to verify that imports are working correctly.
Run this script to check if the package structure is set up properly.
"""


def test_imports():
    """Test that imports work correctly."""
    try:
        # Test importing from image_features
        from image_features import serialization

        print("✅ Successfully imported image_features.serialization")

        # Test importing from images
        from images import downloader

        print("✅ Successfully imported images.downloader")

        # Test importing from listings
        from listings import scraper

        print("✅ Successfully imported listings.scraper")

        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False


if __name__ == "__main__":
    print("Testing imports...")
    success = test_imports()
    if success:
        print("\nAll imports successful! Your package structure is set up correctly.")
    else:
        print(
            "\nSome imports failed. Please check your package structure and PYTHONPATH."
        )
        print("Try running: pip install -e .")
