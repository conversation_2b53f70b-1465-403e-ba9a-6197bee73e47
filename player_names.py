import pandas as pd
from unidecode import unidecode
from tqdm.auto import tqdm
from flashtext import KeywordProcessor
import google.generativeai as genai

def normalize_name(name) -> str:
    return unidecode(name).replace("'", "").replace("-", "").replace(" ", "").replace(".", "").upper()

def split_first_last(full_name:str) -> tuple[str, str]:
    if full_name is None: 
        return (None, None)
    # make sure no parts of the name are lost
    parts = full_name.split()
    if len(parts) == 1:
        return parts[0], ""
    else:
        return parts[0], " ".join(parts[1:])
    

def normalize(text: str | None) -> str:
    if text is None or pd.isna(text):
        return ""

    result = unidecode(str(text)).replace("'", "").replace(".", "").upper()
    new_listing = "NEW LISTING"
    if result.startswith(new_listing):
        result = result[len(new_listing):]

    return result


def swap_first_last(name: str) -> str:
    parts = name.split()
    if len(parts) >= 2:
        parts[0], parts[-1] = parts[-1], parts[0]
        return " ".join(parts)
    else:
        return name

def extract_player_names_from_titles(titles: list[str], known_names: list[str], aliases: dict[str, str]) -> list[str | None]:
    """
    Extract player names from titles using known player names and aliases for matching.

    Parameters:
    -----------
    titles : list[str]
        List of titles to search for player names
    known_names : list[str]
        List of known player names to match against
    aliases : dict[str, str]
        Dictionary mapping aliases/nicknames to canonical player names

    Returns:
    --------
    list[str | None]
        List of player names found in each title. Returns None for titles
        where no player name is found. Length matches input titles list.
    """
    # Normalize known names and add first/last name swapped versions
    normalized_names = [normalize(name) for name in known_names]
    # normalized_names += [swap_first_last(name) for name in normalized_names]

    # Create a mapping from normalized names back to original names
    name_mapping = {}
    for original_name in known_names:
        normalized = normalize(original_name)
        if normalized.strip():
            name_mapping[normalized.upper()] = original_name
            # Also map the swapped version
            swapped = swap_first_last(normalized)
            if swapped.strip():
                name_mapping[swapped.upper()] = original_name

    # Create alias mapping from normalized aliases to original names
    alias_mapping = {}
    for alias, original_name in aliases.items():
        normalized_alias = normalize(alias)
        if normalized_alias.strip():
            alias_mapping[normalized_alias.upper()] = original_name

    # Initialize keyword processors
    name_processor = KeywordProcessor()
    for name in normalized_names:
        if name.strip():  # Only add non-empty names
            name_processor.add_keyword(name.upper())

    alias_processor = KeywordProcessor()
    for alias in alias_mapping.keys():
        if alias.strip():
            alias_processor.add_keyword(alias.upper())

    # Process each title and extract player names
    results = []

    for title in tqdm(titles, desc="Extracting player names"):
        if title is None or pd.isna(title):
            results.append(None)
            continue

        normalized_title = normalize(title)
        if not normalized_title.strip():
            results.append(None)
            continue

        # Find all matching keywords in the title
        # print(normalized_title)
        found_keywords = name_processor.extract_keywords(normalized_title)

        if found_keywords:
            # Return the original name for the first match found
            first_match = found_keywords[0]
            original_name = name_mapping.get(first_match)
            results.append(original_name)
        else:
            found_keywords = alias_processor.extract_keywords(normalized_title)
            if found_keywords:
                first_match = found_keywords[0]
                original_name = alias_mapping.get(first_match)
                results.append(original_name)
            else:
                results.append(None)

    return results


def find_aliases(titles: list[str], known_names: list[str], api_key: str) -> dict[str, str]:
    """
    Find aliases/nicknames for known player names from a list of titles using LLM.

    Args:
        titles: List of titles that may contain player aliases
        known_names: List of known canonical player names
        api_key: Google GenAI API key

    Returns:
        Dictionary mapping aliases to canonical player names
    """
    # Configure the GenAI client
    genai.configure(api_key=api_key)

    # Define the report_aliases tool
    report_aliases_tool = genai.protos.Tool(
        function_declarations=[
            genai.protos.FunctionDeclaration(
                name="report_aliases",
                description="Report discovered player aliases and their canonical names",
                parameters=genai.protos.Schema(
                    type=genai.protos.Type.OBJECT,
                    properties={
                        "aliases": genai.protos.Schema(
                            type=genai.protos.Type.ARRAY,
                            items=genai.protos.Schema(
                                type=genai.protos.Type.OBJECT,
                                properties={
                                    "alias": genai.protos.Schema(
                                        type=genai.protos.Type.STRING,
                                        description="The alias or nickname found"
                                    ),
                                    "canonical_name": genai.protos.Schema(
                                        type=genai.protos.Type.STRING,
                                        description="The canonical player name this alias refers to"
                                    )
                                },
                                required=["alias", "canonical_name"]
                            ),
                            description="List of alias-canonical name pairs"
                        )
                    },
                    required=["aliases"]
                )
            )
        ]
    )

    # Initialize the model with tools
    model = genai.GenerativeModel(
        model_name="gemini-1.5-flash",
        tools=[report_aliases_tool]
    )

    # Create the prompt
    titles_text = "\n".join([f"- {title}" for title in titles[:100]])  # Limit to 100 titles
    known_names_text = "\n".join([f"- {name}" for name in known_names])

    prompt = f"""
    I have a list of known player names and a list of titles that may contain aliases or nicknames
    for these players. Please identify any aliases, nicknames, or alternative names that refer to
    the known players.

    Known Player Names:
    {known_names_text}

    Titles to analyze:
    {titles_text}

    Please look for:
    1. Common nicknames (e.g., "MJ" for "Michael Jordan", "Shaq" for "Shaquille O'Neal")
    2. Shortened versions of names (e.g., "LeBron" for "LeBron James")
    3. Alternative spellings or variations
    4. Popular nicknames or monikers (e.g., "King James", "Black Mamba")

    Use the report_aliases tool to provide a list of aliases you find and their corresponding
    canonical player names. Only include aliases that clearly refer to one of the known players.
    """

    # Generate response
    response = model.generate_content(prompt)

    # Check if the model used the tool
    if response.candidates[0].content.parts:
        for part in response.candidates[0].content.parts:
            if hasattr(part, 'function_call') and part.function_call.name == "report_aliases":
                # Extract the aliases from the function call
                args = dict(part.function_call.args)
                aliases_list = args.get('aliases', [])

                # Convert to dictionary format
                aliases_dict = {}
                for alias_obj in aliases_list:
                    if isinstance(alias_obj, dict):
                        alias = alias_obj.get('alias', '').strip()
                        canonical = alias_obj.get('canonical_name', '').strip()
                        if alias and canonical and canonical in known_names:
                            aliases_dict[alias] = canonical

                return aliases_dict

    # Return empty dict if no aliases found
    return {}


def extract_player_names_llm(titles: list[str], api_key: str) -> list[str]:
    """
    Extract player full names from a list of titles using Google's Gemini GenAI.

    Args:
        titles: List of titles that may contain player names
        api_key: Google GenAI API key

    Returns:
        List of player full names extracted from the titles
    """

    # Configure the GenAI client
    genai.configure(api_key=api_key)

    # Define the report_results tool
    report_results_tool = genai.protos.Tool(
        function_declarations=[
            genai.protos.FunctionDeclaration(
                name="report_results",
                description="Report the extracted player names",
                parameters=genai.protos.Schema(
                    type=genai.protos.Type.OBJECT,
                    properties={
                        "player_names": genai.protos.Schema(
                            type=genai.protos.Type.ARRAY,
                            items=genai.protos.Schema(type=genai.protos.Type.STRING),
                            description="List of full player names extracted from the titles"
                        )
                    },
                    required=["player_names"]
                )
            )
        ]
    )

    # Initialize the model with tools
    model = genai.GenerativeModel(
        model_name="gemini-1.5-flash",
        tools=[report_results_tool]
    )

    # Create the prompt
    titles_text = "\n".join([f"- {title}" for title in titles])
    prompt = f"""
    Please analyze the following titles and extract all player full names mentioned in them.
    First name should come before last name.
    Look for names that appear to be athletes, sports players, or competitors.

    Titles:
    {titles_text}

    Please use the report_results tool to provide a list of the full names of all players
    you can identify from these titles.
    Only include actual player names, not team names, locations, or other non-player entities.
    """

    # Generate response
    response = model.generate_content(prompt)

    # Check if the model used the tool
    if response.candidates[0].content.parts:
        for part in response.candidates[0].content.parts:
            if hasattr(part, 'function_call') and part.function_call.name == "report_results":
                # Extract the player names from the function call
                args = dict(part.function_call.args)
                player_names = args.get('player_names', [])
                return list(player_names)
    else:
        raise RuntimeError("No player names found in the response.", response)
