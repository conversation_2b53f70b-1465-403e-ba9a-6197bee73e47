{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Image downloader debug",
            "type": "debugpy",
            "request": "launch",
            "program": "images/cli.py",
            "console": "integratedTerminal",
            "args": "gs://data-cardcomp/images --max-parallel 1  -l 10 -v"
        },
        {
            "name": "dinov2",
            "type": "debugpy",
            "request": "launch",
            "program": "image_features/cli.py",
            "console": "integratedTerminal",
            "args": "/tmp/images /tmp/out -b 2 -p 2 -w 0"
        }

    ]
}

// image_features/cli.py /tmp/images/ /tmp/out -b 2 -p 2 -w 0