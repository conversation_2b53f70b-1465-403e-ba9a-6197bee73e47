import io
import logging
import os
from pathlib import Path
from typing import List, Tu<PERSON>, Dict, Optional, Union
import torch.multiprocessing as mp

import pandas as pd
import torch
from PIL import Image
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms
from tqdm.auto import tqdm
from transformers import AutoImageProcessor, AutoModel

from image_features.dataset import GCSImageDataset
from image_features.serialization import tensor_to_bytes

logger = logging.getLogger(__name__)


def load_dinov2_model(
    model_name: str = "facebook/dinov2-large",
) -> <PERSON>ple[AutoImageProcessor, torch.nn.Module]:
    """
    Load the DINOv2 model and processor.

    Args:
        model_name: Name of the DINOv2 model to load

    Returns:
        Tuple of (processor, model)
    """
    processor = AutoImageProcessor.from_pretrained(model_name)
    model = AutoModel.from_pretrained(model_name)

    # Ensure model is in eval mode and on the right device
    model.eval()
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)

    return processor, model


def calc_embeddings(
    batch: torch.Tensor, processor: AutoImageProcessor, model: torch.nn.Module
) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
    """
    Calculate DINOv2 embeddings for a batch of images.

    Args:
        batch: Batch of images
        processor: DINOv2 image processor
        model: DINOv2 model

    Returns:
        Tuple of (cls_embeddings, vis_embeddings)
    """
    device = next(model.parameters()).device

    # Process images
    inputs = processor(images=batch, return_tensors="pt").to(device)

    # Forward pass through the model
    with torch.no_grad():
        outputs = model(**inputs)
        embeddings_cls = outputs.last_hidden_state[:, 0]
        embeddings_vis = outputs.last_hidden_state[:, 1:, :].mean(dim=1)

    # Normalize embeddings
    normalized_embedding_cls = torch.nn.functional.normalize(embeddings_cls, dim=1)
    normalized_embedding_vis = torch.nn.functional.normalize(embeddings_vis, dim=1)

    return normalized_embedding_cls, normalized_embedding_vis


def process_images(
    image_dir: str,
    output_path: str,
    batch_size: int = 16,
    partition_size: int = 1000,
    num_workers: int = 4,
) -> int:
    """
    Process images in a directory and save DINOv2 embeddings.

    Args:
        image_dir: Directory containing images (local or remote path)
        output_path: Path to save embeddings (local or remote path)
        batch_size: Batch size for processing
        partition_size: Number of embeddings per output file
        num_workers: Number of workers for data loading

    Returns:
        Number of images processed
    """
    # mp.set_start_method('forkserver') # or 'forkserver'

    # strip trailing /
    output_path = output_path.rstrip()

    # Create dataset and dataloader
    dataset = GCSImageDataset(
        urls_or_dir=image_dir,
        transform=transforms.Compose(
            [transforms.Resize((224, 224)), transforms.ToTensor()]
        ),
    )

    dataloader = DataLoader(
        dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers
    )

    # Load model
    processor, model = load_dinov2_model()
    device = next(model.parameters()).device
    logger.info(f"Using device: {device}")

    # Process batches
    partition_index = 0
    pd_dinov2_df = None
    total_processed = 0

    for batch in tqdm(dataloader, desc="Processing images"):
        # Calculate embeddings
        embeddings_cls_batch, embeddings_vis_batch = calc_embeddings(
            batch["image"], processor, model
        )

        # Split embeddings
        embeddings_cls = torch.split(embeddings_cls_batch, 1)
        embeddings_vis = torch.split(embeddings_vis_batch, 1)
        item_ids = batch["item_id"].tolist()

        # Convert to bytes for storage
        embeddings_cls_bytes = [tensor_to_bytes(e) for e in embeddings_cls]
        embeddings_vis_bytes = [tensor_to_bytes(e) for e in embeddings_vis]

        # Create dataframe
        curr_df = pd.DataFrame(
            {
                "item_id": item_ids,
                "cls": embeddings_cls_bytes,
                "vis": embeddings_vis_bytes,
            }
        )

        # Append to main dataframe
        if pd_dinov2_df is None:
            pd_dinov2_df = curr_df
        else:
            pd_dinov2_df = pd.concat([pd_dinov2_df, curr_df])

        total_processed += len(item_ids)

        # Save partition if it's full
        if pd_dinov2_df is not None and pd_dinov2_df.shape[0] >= partition_size:
            output_file = f"{output_path}/{partition_index}.parquet"
            logger.info(f"Saving partition {partition_index} to {output_file}")
            pd_dinov2_df.to_parquet(output_file)
            partition_index += 1
            pd_dinov2_df = None

    # Save any remaining embeddings
    if pd_dinov2_df is not None:
        output_file = f"{output_path}/{partition_index}.parquet"
        logger.info(f"Saving final partition {partition_index} to {output_file}")
        pd_dinov2_df.to_parquet(output_file)

    return total_processed
