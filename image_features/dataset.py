import io

import fsspec
import gcsfs
import torch
from PIL import Image
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms


class GCSImageDataset(Dataset):
    def __init__(
        self, urls_or_dir: list[str] | str, limit: int = 10**20, transform=None
    ):

        if isinstance(urls_or_dir, str):
            fs, path = fsspec.core.url_to_fs(urls_or_dir)
            paths = fs.ls(path, detail=False)
            paths = [p for p in paths if p.endswith(".webp")]
        else:
            assert isinstance(urls_or_dir, list)
            fs, _ = fsspec.core.url_to_fs(urls_or_dir[0])
            paths = urls_or_dir

        self._fs = None
        protocol = fs.protocol

        # If the protocol is a list (some FS return a list), pick the first
        if isinstance(protocol, tuple):
            protocol = protocol[0]

        self._protocol = protocol

        self.file_paths = paths[:limit]
        self.transform = transform or transforms.ToTensor()

    def __len__(self):
        return len(self.file_paths)

    def __getitem__(self, idx):
        # Filename format is "images/110601915237.webp",
        # get item_id from filename
        if self._fs is None:
            self._fs = fsspec.filesystem(self._protocol)

        item_id = int(self.file_paths[idx].split("/")[-1].split(".")[0])
        with self._fs.open(self.file_paths[idx], "rb") as f:
            img_bytes = f.read()
        img = Image.open(io.BytesIO(img_bytes)).convert("RGB")
        return {"image": self.transform(img), "item_id": item_id}
