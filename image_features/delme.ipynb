{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6aa4afbd", "metadata": {}, "outputs": [], "source": ["# autoreload\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 15, "id": "8d3ac0cf", "metadata": {}, "outputs": [], "source": ["\n", "from image_features.dataset import GCSImageDataset\n", "from torch.utils.data import Dataset, DataLoader\n", "from torchvision import transforms\n", "\n", "\n", "bucket = \"data-cardcomp\"  # no \"gs://\" prefix\n", "image_paths = [\n", "    \"gs://data-cardcomp/images/110601915237.webp\",\n", "    \"gs://data-cardcomp/images/112144587713.webp\",\n", "    \"gs://data-cardcomp/images/112144860779.webp\"\n", "]\n", "\n", "transform = transforms.Compose([\n", "    transforms.Resize((224, 224)),\n", "    transforms.ToTensor()\n", "])\n", "\n", "\n", "dataset = GCSImageDataset(image_paths, 2, transform)\n", "# dataset = GCSImageDataset(bucket, image_paths, transform=transform, limit=1)\n", "\n", "dataloader = DataLoader(dataset, batch_size=2, shuffle=True, num_workers=0)\n"]}, {"cell_type": "code", "execution_count": 16, "id": "771f4b0a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'image': tensor([[[[0.2902, 0.3059, 0.3765,  ..., 0.6745, 0.6784, 0.6784],\n", "          [0.2941, 0.3176, 0.3765,  ..., 0.6706, 0.6745, 0.6745],\n", "          [0.3059, 0.3294, 0.3725,  ..., 0.6627, 0.6667, 0.6706],\n", "          ...,\n", "          [0.1686, 0.2510, 0.3059,  ..., 0.6118, 0.6235, 0.6353],\n", "          [0.1490, 0.2314, 0.2980,  ..., 0.6235, 0.6314, 0.6471],\n", "          [0.1529, 0.2353, 0.3137,  ..., 0.6824, 0.6902, 0.6980]],\n", "\n", "         [[0.2902, 0.3098, 0.3765,  ..., 0.6824, 0.6863, 0.6863],\n", "          [0.2941, 0.3176, 0.3765,  ..., 0.6784, 0.6824, 0.6824],\n", "          [0.3059, 0.3294, 0.3765,  ..., 0.6667, 0.6706, 0.6784],\n", "          ...,\n", "          [0.1647, 0.2471, 0.3059,  ..., 0.6314, 0.6431, 0.6510],\n", "          [0.1490, 0.2314, 0.2941,  ..., 0.6353, 0.6471, 0.6549],\n", "          [0.1529, 0.2314, 0.3137,  ..., 0.6941, 0.7020, 0.7059]],\n", "\n", "         [[0.3333, 0.3529, 0.4196,  ..., 0.6980, 0.7020, 0.7020],\n", "          [0.3412, 0.3608, 0.4196,  ..., 0.6941, 0.6980, 0.6980],\n", "          [0.3490, 0.3725, 0.4157,  ..., 0.6824, 0.6863, 0.6941],\n", "          ...,\n", "          [0.1765, 0.2588, 0.3137,  ..., 0.6235, 0.6353, 0.6471],\n", "          [0.1569, 0.2392, 0.3059,  ..., 0.6314, 0.6431, 0.6510],\n", "          [0.1608, 0.2431, 0.3216,  ..., 0.6902, 0.6980, 0.7020]]],\n", "\n", "\n", "        [[[0.7137, 0.7137, 0.7137,  ..., 0.7569, 0.7529, 0.7529],\n", "          [0.7137, 0.7137, 0.7137,  ..., 0.7569, 0.7529, 0.7529],\n", "          [0.7137, 0.7137, 0.7137,  ..., 0.7569, 0.7529, 0.7529],\n", "          ...,\n", "          [0.8039, 0.8039, 0.8039,  ..., 0.8431, 0.8431, 0.8431],\n", "          [0.8039, 0.8039, 0.8039,  ..., 0.8431, 0.8431, 0.8431],\n", "          [0.8039, 0.8039, 0.8039,  ..., 0.8431, 0.8431, 0.8431]],\n", "\n", "         [[0.7412, 0.7412, 0.7412,  ..., 0.7647, 0.7608, 0.7608],\n", "          [0.7412, 0.7412, 0.7412,  ..., 0.7647, 0.7608, 0.7608],\n", "          [0.7412, 0.7412, 0.7412,  ..., 0.7647, 0.7608, 0.7608],\n", "          ...,\n", "          [0.8235, 0.8235, 0.8235,  ..., 0.8510, 0.8510, 0.8510],\n", "          [0.8235, 0.8235, 0.8235,  ..., 0.8510, 0.8510, 0.8510],\n", "          [0.8235, 0.8235, 0.8235,  ..., 0.8510, 0.8510, 0.8510]],\n", "\n", "         [[0.7294, 0.7294, 0.7294,  ..., 0.7608, 0.7569, 0.7569],\n", "          [0.7294, 0.7294, 0.7294,  ..., 0.7608, 0.7569, 0.7569],\n", "          [0.7294, 0.7294, 0.7294,  ..., 0.7608, 0.7569, 0.7569],\n", "          ...,\n", "          [0.8157, 0.8157, 0.8196,  ..., 0.8588, 0.8588, 0.8588],\n", "          [0.8157, 0.8157, 0.8196,  ..., 0.8588, 0.8588, 0.8588],\n", "          [0.8157, 0.8157, 0.8196,  ..., 0.8588, 0.8588, 0.8588]]]]), 'item_id': tensor([112144587713, 110601915237])}\n"]}], "source": ["for images in dataloader:\n", "    print(images)\n", "    break\n"]}, {"cell_type": "code", "execution_count": 17, "id": "5d00a77f", "metadata": {}, "outputs": [{"data": {"text/plain": ["['gs://data-cardcomp/images/110601915237.webp',\n", " 'gs://data-cardcomp/images/112144587713.webp']"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset.file_paths"]}, {"cell_type": "code", "execution_count": 18, "id": "c6d3a7be", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["`use_fast` is set to `True` but the image processor class does not have a fast version.  Falling back to the slow version.\n"]}], "source": ["import torch\n", "from torchvision import transforms\n", "from PIL import Image\n", "from transformers import AutoImageProcessor, AutoModel\n", "from tqdm.auto import tqdm\n", "import torch\n", "\n", "processor = AutoImageProcessor.from_pretrained(\"facebook/dinov2-large\", use_fast=True)\n", "model = AutoModel.from_pretrained(\"facebook/dinov2-large\")\n", "\n", "# Ensure model is in eval mode and on the right device\n", "model.eval()\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "model.to(device);\n"]}, {"cell_type": "code", "execution_count": 9, "id": "56160953", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "from image_features.serialization import tensor_to_bytes\n", "batch_size = 250\n", "batch = []\n", "\n", "def calc_embeddings(batch):\n", "    inputs = processor(images=batch, return_tensors=\"pt\").to(device)\n", "\n", "    # Forward pass through the model\n", "    with torch.no_grad():\n", "        outputs = model(**inputs)\n", "        embeddings_cls = outputs.last_hidden_state[:, 0]\n", "        embeddings_vis = outputs.last_hidden_state[:, 1:, :].mean(dim=1)\n", "\n", "    # Normalize (optional but recommended for similarity search)\n", "    normalized_embedding_cls = torch.nn.functional.normalize(embeddings_cls, dim=1)\n", "    normalized_embedding_vis = torch.nn.functional.normalize(embeddings_vis, dim=1)\n", "    return normalized_embedding_cls, normalized_embedding_vis\n", "\n", "\n", "\n", "\n", "partition_size = 1000\n", "partition_index = 0\n", "\n", "pd_dinov2_df = None\n", "for batch in dataloader:\n", "\n", "  embeddings_cls_batch, embeddings_vis_batch = calc_embeddings(batch[\"image\"])\n", "  \n", "  embeddings_cls = torch.split(embeddings_cls_batch, 1)\n", "  embeddings_vis = torch.split(embeddings_vis_batch, 1)\n", "  \n", "\n", "  embeddings_cls_bytes = [tensor_to_bytes(e) for e in embeddings_cls]\n", "  embeddings_vis_bytes = [tensor_to_bytes(e) for e in embeddings_vis]\n", "  curr_df = pd.DataFrame({\"item_id\": batch[\"item_id\"], \"cls\": embeddings_cls_bytes, \"vis\": embeddings_vis_bytes})\n", "\n", "  if pd_dinov2_df is None:\n", "    pd_dinov2_df = curr_df\n", "  else:\n", "    pd_dinov2_df = pd.concat([pd_dinov2_df, curr_df])\n", "\n", "\n", "  if pd_dinov2_df.shape[0] >= partition_size:\n", "    # pd_dinov2_df.to_parquet(f\"gs://data-cardcomp/dinov2/{partition_index}.parquet\")\n", "    pd_dinov2_df.to_parquet(f\"/tmp/{partition_index}.parquet\")\n", "    partition_index += 1\n", "    pd_dinov2_df = None\n", "\n", "if pd_dinov2_df is not None:\n", "  pd_dinov2_df.to_parquet(f\"/tmp/{partition_index}.parquet\")\n", "  partition_index += 1\n", "  pd_dinov2_df = None\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "id": "296c5160", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>cls</th>\n", "      <th>vis</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>112144587713</td>\n", "      <td>b'\\x127/\\xbc\\xbfqU;Y\\xba\\xc7\\xbbu~w\\xbc\\x14* \\...</td>\n", "      <td>b'6\\xc3\\xb8&lt;\\x07\\xe4p\\xbct\\xe8\\xd8\\xbbo\\xbcA;y...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>110601915237</td>\n", "      <td>b'=\\xc6\\xf5&lt;/\\xc1\\x19&lt;#\\x06\\xde\\xbc[\\x8eS&lt;\\xe9...</td>\n", "      <td>b'}\\xccA=\\x00\\xd6Z\\xbb7!z\\xbcH\\x1e\\x86\\xbc\\xd0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>112144860779</td>\n", "      <td>b'B&lt;\\x07\\xbb\\x8cd\\x02\\xbc3\\xa7{\\xbc\\xc1\\rk\\xbc...</td>\n", "      <td>b'1i\\x12=\\x02\\xbdp\\xba\\xf0\\xba\\x83\\xbc\\xb5\\xd2...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        item_id                                                cls  \\\n", "0  112144587713  b'\\x127/\\xbc\\xbfqU;Y\\xba\\xc7\\xbbu~w\\xbc\\x14* \\...   \n", "1  110601915237  b'=\\xc6\\xf5</\\xc1\\x19<#\\x06\\xde\\xbc[\\x8eS<\\xe9...   \n", "0  112144860779  b'B<\\x07\\xbb\\x8cd\\x02\\xbc3\\xa7{\\xbc\\xc1\\rk\\xbc...   \n", "\n", "                                                 vis  \n", "0  b'6\\xc3\\xb8<\\x07\\xe4p\\xbct\\xe8\\xd8\\xbbo\\xbcA;y...  \n", "1  b'}\\xccA=\\x00\\xd6Z\\xbb7!z\\xbcH\\x1e\\x86\\xbc\\xd0...  \n", "0  b'1i\\x12=\\x02\\xbdp\\xba\\xf0\\xba\\x83\\xbc\\xb5\\xd2...  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["pd_dinov2_df"]}, {"cell_type": "code", "execution_count": null, "id": "1b05372f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "cardcomp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}