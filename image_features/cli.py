#!/usr/bin/env python3
import logging
import os
from datetime import datetime
from urllib.parse import urlparse

import click
import fsspec

from image_features.dinov2 import process_images

logger = logging.getLogger(__name__)


def generate_default_output_path() -> str:
    """Generate a default output path with timestamp."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"gs://data-cardcomp/dinov2/embeddings_{timestamp}"


def ensure_directory_exists(path: str) -> None:
    """
    Ensure the directory exists, creating it if necessary.
    Works with both local and remote paths via fsspec.

    Args:
        path: Directory path (local or remote)
    """
    fs, fs_path = fsspec.core.url_to_fs(path)
    # Check if the directory exists
    if fs.protocol[0] == "file" and not fs.exists(fs_path):
        logger.info(f"Creating directory: {path}")
        fs.makedirs(fs_path, exist_ok=True)


@click.command()
@click.argument("image-dir", type=str)
@click.argument("output-dir", type=str)
@click.option(
    "--batch-size", "-b", default=16, type=int, help="Batch size for processing"
)
@click.option(
    "--partition-size",
    "-p",
    default=1000,
    type=int,
    help="Number of embeddings per output file",
)
@click.option(
    "--num-workers",
    "-w",
    default=4,
    type=int,
    help="Number of workers for data loading",
)
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose logging")
def main(
    image_dir: str,
    output_dir: str | None,
    batch_size: int,
    partition_size: int,
    num_workers: int,
    verbose: bool,
) -> None:
    """
    Calculate DINOv2 embeddings for images in a directory.

    This tool processes images from the specified directory (local or remote)
    and saves DINOv2 embeddings as parquet files to the output directory.

    IMAGE-DIR can be either a local path or a remote URL (gs://, s3://, etc.)

    Examples:
      - Process local images: dinov2-cli /path/to/images
      - Process GCS images: dinov2-cli gs://my-bucket/images
      - Specify output: dinov2-cli gs://my-bucket/images -o gs://my-bucket/embeddings
    """
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG if verbose else logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # Set default output path if none provided
    if output_dir is None:
        output_dir = generate_default_output_path()

    # If path doesn't have a protocol prefix, assume it's a local file
    if "://" not in output_dir:
        output_dir = f"file://{os.path.abspath(output_dir)}"

    # Ensure output directory exists
    ensure_directory_exists(output_dir)

    # Log configuration
    logger.info(f"Starting DINOv2 embedding calculation")
    logger.info(f"Image directory: {image_dir}")
    logger.info(f"Output directory: {output_dir}")
    logger.info(f"Batch size: {batch_size}")
    logger.info(f"Partition size: {partition_size}")
    logger.info(f"Number of workers: {num_workers}")

    # Process images
    total_processed = process_images(
        image_dir=image_dir,
        output_path=output_dir,
        batch_size=batch_size,
        partition_size=partition_size,
        num_workers=num_workers,
    )

    # Log results
    logger.info(f"Successfully processed {total_processed} images")
    logger.info(f"Embeddings saved to {output_dir}")


if __name__ == "__main__":
    main()
