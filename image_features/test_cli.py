import os
from unittest.mock import MagicMock, patch

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from image_features.cli import (
    ensure_directory_exists,
    generate_default_output_path,
    main,
)


def test_generate_default_output_path():
    """Test that default output path is generated with timestamp."""
    path = generate_default_output_path()
    assert path.startswith("gs://data-cardcomp/dinov2/embeddings_")
    assert len(path) > len("gs://data-cardcomp/dinov2/embeddings_")


@pytest.fixture
def mock_fsspec():
    with patch("fsspec.core.url_to_fs") as mock_url_to_fs:
        fs_mock = MagicMock()
        mock_url_to_fs.return_value = (fs_mock, "path/to/dir")
        yield fs_mock


def test_ensure_directory_exists_creates_dir(mock_fsspec):
    """Test that ensure_directory_exists creates directory if it doesn't exist."""
    mock_fsspec.exists.return_value = False

    ensure_directory_exists("gs://bucket/path")

    mock_fsspec.exists.assert_called_once_with("path/to/dir")
    mock_fsspec.makedirs.assert_called_once_with("path/to/dir", exist_ok=True)


def test_ensure_directory_exists_skips_if_exists(mock_fsspec):
    """Test that ensure_directory_exists doesn't create directory if it exists."""
    mock_fsspec.exists.return_value = True

    ensure_directory_exists("gs://bucket/path")

    mock_fsspec.exists.assert_called_once_with("path/to/dir")
    mock_fsspec.makedirs.assert_not_called()


@patch("image_features.cli.process_images")
def test_cli_with_minimal_args(mock_process_images):
    """Test CLI with minimal arguments."""
    mock_process_images.return_value = 10

    runner = CliRunner()
    result = runner.invoke(main, ["gs://bucket/images"])

    assert result.exit_code == 0
    assert "Starting DINOv2 embedding calculation" in result.output
    assert "Successfully processed 10 images" in result.output

    # Check that process_images was called with correct arguments
    mock_process_images.assert_called_once()
    args, kwargs = mock_process_images.call_args
    assert kwargs["image_dir"] == "gs://bucket/images"
    assert kwargs["batch_size"] == 16  # default
    assert kwargs["partition_size"] == 1000  # default
    assert kwargs["num_workers"] == 4  # default


@patch("image_features.cli.process_images")
def test_cli_with_all_options(mock_process_images):
    """Test CLI with all options specified."""
    mock_process_images.return_value = 5

    runner = CliRunner()
    result = runner.invoke(
        main,
        [
            "gs://bucket/images",
            "--output-dir",
            "gs://bucket/output",
            "--batch-size",
            "8",
            "--partition-size",
            "500",
            "--num-workers",
            "2",
            "--verbose",
        ],
    )

    assert result.exit_code == 0
    assert "Successfully processed 5 images" in result.output

    # Check that process_images was called with correct arguments
    mock_process_images.assert_called_once()
    args, kwargs = mock_process_images.call_args
    assert kwargs["image_dir"] == "gs://bucket/images"
    assert kwargs["output_path"] == "gs://bucket/output"
    assert kwargs["batch_size"] == 8
    assert kwargs["partition_size"] == 500
    assert kwargs["num_workers"] == 2
