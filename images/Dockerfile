FROM python:3.12-slim

WORKDIR /app

# Copy requirements first to leverage caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy only the application code (changes frequently)
COPY images /app/images

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# # Default command - can be overridden by Cloud Run Job
# ENTRYPOINT ["python", "-m", "images.cli"]
