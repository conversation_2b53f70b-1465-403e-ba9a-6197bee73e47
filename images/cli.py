#!/usr/bin/env python3
import asyncio
import logging
import os

import click
import fsspec
import pandas as pd
import pandas_gbq

from images.downloader import download_resources

logger = logging.getLogger(__name__)


def get_downloaded_item_ids(output_dir: str) -> set[int]:
    """
    Get set of item IDs that have already been downloaded.
    Memory-optimized version that processes files in chunks.

    Args:
        output_dir: Path to output directory (local or remote)

    Returns:
        Set of item IDs already downloaded
    """
    # Use fsspec to handle both local and remote paths
    fs, path = fsspec.core.url_to_fs(output_dir)

    # Use glob to filter files during listing (more efficient)
    logger.info("Listing image files in output directory")

    # Fallback to ls + filter if glob fails
    files = fs.ls(path, detail=False)
    files = [f for f in files if f.endswith(".webp")]
    logger.info(f"Found {len(files)} .webp files using ls")

    # Process all files
    item_ids = set()

    for f in files:
        # Extract item_id from filename (remove extension)
        filename = os.path.basename(f)
        item_id_str = os.path.splitext(filename)[0]
        item_ids.add(int(item_id_str))

    logger.info(f"Found {len(item_ids)} downloaded item IDs")
    return item_ids


async def process_all_bigquery_rows(
    output_dir: str, max_parallel: int, limit: int | None = None
) -> int:
    """
    Process a single parquet file and download images.

    Args:
        output_dir: Directory to save images
        max_parallel: Maximum number of parallel downloads

    Returns:
        Number of images downloaded
    """
    # Read the parquet file - pandas handles storage options automatically

    project_id = "cardcomp"

    # Step 1: Get only item_ids first (small memory footprint)
    logger.info("Querying item_ids from cardcomp.ebay.scraped-ebay-view")
    df_ids = pandas_gbq.read_gbq(
        "SELECT item_id FROM `cardcomp.ebay.scraped-ebay-view`",
        project_id=project_id,
    )
    assert isinstance(df_ids, pd.DataFrame)

    all_item_ids = set(df_ids['item_id'])
    click.echo(f"Found {len(all_item_ids)} total items in database")

    # Step 2: Get downloaded IDs efficiently by listing directory once
    logger.info("Getting list of already downloaded files")
    downloaded_ids = get_downloaded_item_ids(output_dir)
    logger.info(f"Found {len(downloaded_ids)} already downloaded")

    # Step 2b: Filter to get items that need downloading
    items_to_download = list(all_item_ids - downloaded_ids)

    # Apply limit
    if limit is not None:
        items_to_download = items_to_download[:limit]

    click.echo(f"Need to download {len(items_to_download)} items")

    if not items_to_download:
        click.echo("No images to download")
        return 0

    # Step 3: Only now get image_urls for items that need downloading
    logger.info("Querying image_urls for items that need downloading")
    item_ids_str = ','.join(map(str, items_to_download))
    query = (
        f"SELECT item_id, image_url FROM `cardcomp.ebay.scraped-ebay-view` "
        f"WHERE item_id IN ({item_ids_str})"
    )
    df_urls = pandas_gbq.read_gbq(query, project_id=project_id)
    assert isinstance(df_urls, pd.DataFrame)

    click.echo(f"Downloading {len(df_urls)} images to {output_dir}")

    # Download images
    results = await download_resources(
        urls=df_urls.image_url.tolist(),
        item_ids=df_urls.item_id.tolist(),
        output_path=output_dir,
        max_parallel=max_parallel,
    )

    return len(results)


@click.command()
@click.argument("output-dir")
@click.option(
    "--max-parallel", "-m", default=50, help="Maximum number of parallel downloads"
)
@click.option(
    "--limit", "-l", default=None, type=int, help="Limit the number of rows to process"
)
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose logging")
def main(output_dir: str, max_parallel: int, limit: int | None, verbose: bool) -> None:
    """
    Download eBay images from parquet datasets.

    This tool scans parquet files for image URLs and item IDs, then downloads
    all images that haven't been downloaded yet to the specified output directory.

    output-dir can be either local paths or remote URLs (gs://, s3://, etc.)
    """
    # Ensure output directory exists

    logging.basicConfig(level=(logging.DEBUG if verbose else logging.INFO))
    logging.info(
        f"Starting image downloader. Output directory: {output_dir}, max_parallel: {max_parallel}"
    )

    total_downloaded = asyncio.run(
        process_all_bigquery_rows(output_dir, max_parallel, limit)
    )

    click.echo(f"Successfully downloaded {total_downloaded} images to {output_dir}")


if __name__ == "__main__":
    main()
