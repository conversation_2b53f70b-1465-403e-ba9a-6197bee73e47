import os
from unittest.mock import AsyncMock, MagicMock, patch

import pandas as pd
import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from images import cli
from images.cli import (
    get_downloaded_item_ids,
    main,
    process_all_bigquery_rows,
)


# Tests for get_downloaded_item_ids
@pytest.fixture
def mock_fsspec():
    with patch("fsspec.core.url_to_fs") as mock_url_to_fs:
        fs_mock = MagicMock()
        mock_url_to_fs.return_value = (fs_mock, "path/to/dir")
        yield fs_mock


def test_get_downloaded_item_ids_local(mock_fsspec):
    """Test getting downloaded item IDs from a local directory."""
    # Setup mock filesystem with some files
    mock_fsspec.glob.return_value = [
        "path/to/dir/123.webp",
        "path/to/dir/456.webp",
        "path/to/dir/789.webp",
    ]

    # Call the function
    result = get_downloaded_item_ids("/tmp/downloads")

    # Verify the results
    assert result == {123, 456, 789}
    mock_fsspec.glob.assert_called_once_with(os.path.join("path/to/dir", "*.webp"))


def test_get_downloaded_item_ids_remote(mock_fsspec):
    """Test getting downloaded item IDs from a remote directory (GCS)."""
    # Setup mock filesystem with some files
    mock_fsspec.glob.return_value = [
        "gs://bucket/images/123.webp",
        "gs://bucket/images/456.webp",
    ]

    # Call the function
    result = get_downloaded_item_ids("gs://bucket/images")

    # Verify the results
    assert result == {123, 456}
    mock_fsspec.glob.assert_called_once_with(os.path.join("path/to/dir", "*.webp"))


def test_get_downloaded_item_ids_empty(mock_fsspec):
    """Test getting downloaded item IDs from an empty directory."""
    # Setup mock filesystem with no files
    mock_fsspec.glob.return_value = []

    # Call the function
    result = get_downloaded_item_ids("/tmp/downloads")

    # Verify the results
    assert result == set()
    mock_fsspec.glob.assert_called_once_with(os.path.join("path/to/dir", "*.webp"))


# Tests for process_all_bigquery_rows
@pytest.fixture
def mock_pandas_gbq():
    with patch("pandas_gbq.read_gbq") as mock_read_gbq:
        # Create a sample DataFrame
        df = pd.DataFrame(
            {
                "item_id": [123, 456, 789],
                "image_url": [
                    "http://example.com/123.jpg",
                    "http://example.com/456.jpg",
                    "http://example.com/789.jpg",
                ],
            }
        )
        mock_read_gbq.return_value = df
        yield mock_read_gbq


@pytest.fixture
def mock_get_downloaded_ids():
    with patch("images.cli.get_downloaded_item_ids") as mock:
        yield mock


@pytest.fixture
def mock_download_resources():
    with patch("images.cli.download_resources") as mock:
        # Make it an async mock that returns a list of paths
        async_mock = AsyncMock()
        async_mock.return_value = [
            "/tmp/downloads/123.jpg",
            "/tmp/downloads/456.jpg",
        ]
        mock.return_value = async_mock.return_value
        yield mock


@pytest.mark.asyncio
async def test_process_all_bigquery_rows_new_downloads(
    mock_pandas_gbq, mock_get_downloaded_ids, mock_download_resources
):
    """Test processing rows with new images to download."""
    # Setup mocks
    mock_get_downloaded_ids.return_value = {789}  # One image already downloaded

    # Call the function
    result = await process_all_bigquery_rows("/tmp/downloads", 10)

    # Verify the results
    assert result == 2  # Two new images downloaded
    mock_pandas_gbq.assert_called_once()
    mock_get_downloaded_ids.assert_called_once_with("/tmp/downloads")
    mock_download_resources.assert_called_once()
    # Check that only the non-downloaded images were passed to download_resources
    call_kwargs = mock_download_resources.call_args[1]
    urls, item_ids = call_kwargs["urls"], call_kwargs["item_ids"]
    assert set(item_ids) == {123, 456}
    assert len(urls) == 2


@pytest.mark.asyncio
async def test_process_all_bigquery_rows_with_limit(
    mock_pandas_gbq, mock_get_downloaded_ids, mock_download_resources
):
    """Test processing rows with a limit."""
    # Setup mocks
    mock_get_downloaded_ids.return_value = set()  # No images downloaded yet

    # Call the function with a limit
    result = await process_all_bigquery_rows("/tmp/downloads", 10, limit=1)

    # Verify the results
    assert result == 2  # Two images downloaded (from our mock)
    mock_pandas_gbq.assert_called_once()
    mock_get_downloaded_ids.assert_called_once_with("/tmp/downloads")
    mock_download_resources.assert_called_once()
    # Check that only the limited number of images were passed
    call_kwargs = mock_download_resources.call_args[1]
    urls, item_ids = call_kwargs["urls"], call_kwargs["item_ids"]
    assert len(urls) == 1
    assert len(item_ids) == 1


@pytest.mark.asyncio
async def test_process_all_bigquery_rows_all_downloaded(
    mock_pandas_gbq, mock_get_downloaded_ids, mock_download_resources
):
    """Test processing rows when all images are already downloaded."""
    # Setup mocks
    mock_get_downloaded_ids.return_value = {
        123,
        456,
        789,
    }  # All images already downloaded

    # Call the function
    result = await process_all_bigquery_rows("/tmp/downloads", 10)

    # Verify the results
    assert result == 0  # No new images downloaded
    mock_pandas_gbq.assert_called_once()
    mock_get_downloaded_ids.assert_called_once_with("/tmp/downloads")
    mock_download_resources.assert_not_called()


# Tests for the CLI command
@pytest.fixture
def mock_process_rows():
    with patch("images.cli.process_all_bigquery_rows") as mock:
        # Make it an async mock that returns a count
        async_mock = AsyncMock()
        async_mock.return_value = 5
        mock.return_value = async_mock.return_value
        yield mock


def test_main_command_success(mock_process_rows):
    """Test the main CLI command with successful execution."""
    runner = CliRunner()
    result = runner.invoke(main, ["/tmp/downloads", "--max-parallel", "10"])

    # Verify the results
    assert result.exit_code == 0
    assert "Successfully downloaded 5 images" in result.output
    mock_process_rows.assert_called_once()


def test_main_command_with_options(mock_process_rows):
    """Test the main CLI command with various options."""
    runner = CliRunner()
    result = runner.invoke(
        main, ["/tmp/downloads", "--max-parallel", "20", "--limit", "10", "--verbose"]
    )

    # Verify the results
    assert result.exit_code == 0
    assert "Successfully downloaded 5 images" in result.output
    mock_process_rows.assert_called_once_with("/tmp/downloads", 20, 10)


def test_main_command_error(mock_process_rows):
    """Test the main CLI command with an error during execution."""
    # Make the process_rows function raise an exception
    mock_process_rows.side_effect = Exception("Test error")

    runner = CliRunner()
    result = runner.invoke(main, ["/tmp/downloads"])

    # Verify the results
    assert result.exit_code != 0
    assert isinstance(result.exception, Exception)


def test_main_module():
    """Test the __main__ block."""
    # A simpler approach to test the __main__ block
    with (
        patch.object(cli, "__name__", "__main__"),
        patch("images.cli.main") as mock_main,
    ):
        # Re-execute the last line of the file which should be: if __name__ == "__main__": main()
        with open("images/cli.py", "r") as f:
            last_line = f.readlines()[-1].strip()

        # Only execute if it's the expected pattern
        if 'if __name__ == "__main__":' in last_line:
            exec(last_line)
            # Check that main was called
            mock_main.assert_called_once()
