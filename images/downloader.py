import asyncio
import logging
import os
from typing import List, Sequence
from urllib.parse import urlparse

import aiohttp
import fsspec

# Configure logger
logger = logging.getLogger(__name__)


class DownloadError(Exception):
    """Exception raised when a download fails."""

    pass


def _get_file_extension(url: str, content_type: str | None) -> str:
    """
    Determine file extension from content-type or URL.

    Args:
        url: The URL of the resource
        content_type: The content type from HTTP headers

    Returns:
        The file extension with leading dot

    Raises:
        ValueError: If the extension cannot be determined
    """
    if content_type:
        if "image/jpeg" in content_type or "image/jpg" in content_type:
            return ".jpg"
        elif "image/png" in content_type:
            return ".png"
        elif "image/webp" in content_type:
            return ".webp"

    # Try to get extension from URL
    path = urlparse(url).path
    ext = os.path.splitext(path)[1]
    if ext:
        return ext

    # Raise if no extension could be determined
    raise ValueError(f"Could not determine file extension for URL: {url}")


async def _download_single_resource(
    session: aiohttp.ClientSession, url: str, item_id: int, output_path: str
) -> str:
    """
    Download a single resource and save it to the specified path.

    Args:
        session: The aiohttp client session
        url: The URL to download
        item_id: The item ID for the filename
        output_path: The directory to save the file

    Returns:
        The full path where the file was saved

    Raises:
        DownloadError: If the download fails
    """
    try:
        output_path = output_path.rstrip("/")
        timeout = aiohttp.ClientTimeout(total=30)
        async with session.get(url, timeout=timeout) as response:
            if response.status != 200:
                raise DownloadError(f"Error downloading {url}: HTTP {response.status}")

            # Determine file extension
            content_type = response.headers.get("Content-Type")
            ext = _get_file_extension(url, content_type)

            # Create full output path
            full_path = f"{output_path}/{item_id}{ext}"

            # Read content
            content = await response.read()

            # Write to storage (local or cloud)
            # Type annotation to help pyright understand fsspec.open returns a file-like object
            with fsspec.open(full_path, "wb") as f:  # type: ignore
                f.write(content)  # type: ignore

            logger.debug(f"Successfully downloaded {url} to {full_path}")
            return full_path
    except (aiohttp.ClientError, asyncio.TimeoutError) as e:
        raise DownloadError(f"Failed to download {url}: {str(e)}")


async def _download_resources_async(
    urls: Sequence[str], item_ids: Sequence[int], output_path: str, max_parallel: int
) -> List[str]:
    """
    Download resources asynchronously with a limit on parallel downloads.

    Args:
        urls: Sequence of URLs to download
        item_ids: Sequence of item IDs for filenames
        output_path: Directory to save files
        max_parallel: Maximum number of parallel downloads

    Returns:
        List of paths where resources were saved

    Raises:
        DownloadError: If any download fails
    """
    # Create semaphore to limit concurrent downloads
    semaphore = asyncio.Semaphore(max_parallel)
    results: List[str] = []

    async def download_with_semaphore(url: str, item_id: int) -> str:
        async with semaphore:
            return await _download_single_resource(session, url, item_id, output_path)

    # Set up connection pooling
    conn = aiohttp.TCPConnector(limit=max_parallel)
    timeout = aiohttp.ClientTimeout(total=60)

    async with aiohttp.ClientSession(connector=conn, timeout=timeout) as session:
        for i, (url, item_id) in enumerate(zip(urls, item_ids)):
            try:
                path = await download_with_semaphore(url, item_id)
                results.append(path)
            except Exception as e:
                logger.error(
                    f"Error downloading resource {i + 1}/{len(urls)}: {str(e)}"
                )
                raise

    return results


async def download_resources(
    urls: Sequence[str],
    item_ids: Sequence[int],
    max_parallel: int,
    output_path: str,
) -> List[str]:
    """
    Download resources in parallel and store them to the specified path.

    Args:
        urls: Sequence of URLs to download
        item_ids: Sequence of item IDs (used for naming the downloaded files)
        max_parallel: Maximum number of parallel downloads
        output_path: Output path (can be local or a GCS bucket path)

    Returns:
        List of paths where resources were saved

    Raises:
        ValueError: If URLs and item_ids have different lengths
        DownloadError: If any download fails
    """
    if len(urls) != len(item_ids):
        raise ValueError("URLs and item_ids must have the same length")

    # Ensure output path exists for local paths
    if not output_path.startswith("gs://"):
        os.makedirs(output_path, exist_ok=True)

    # Run the async download function
    return await _download_resources_async(
        urls=urls, item_ids=item_ids, output_path=output_path, max_parallel=max_parallel
    )
