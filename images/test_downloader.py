from unittest.mock import AsyncMock, MagicMock, patch

import aiohttp
import pytest

from images.downloader import (
    DownloadError,
    _download_resources_async,
    _download_single_resource,
    _get_file_extension,
    download_resources,
)


# Tests for _get_file_extension
@pytest.mark.parametrize(
    "url, content_type, expected",
    [
        ("http://example.com/image", "image/jpeg", ".jpg"),
        ("http://example.com/image", "image/jpg", ".jpg"),
        ("http://example.com/image", "image/png", ".png"),
        ("http://example.com/image", "image/webp", ".webp"),
        ("http://example.com/image.jpg", None, ".jpg"),
        ("http://example.com/image.png", None, ".png"),
        ("http://example.com/image.webp", None, ".webp"),
    ],
)
def test_get_file_extension(url, content_type, expected):
    assert _get_file_extension(url, content_type) == expected


def test_no_extension():
    with pytest.raises(ValueError, match="Could not determine file extension"):
        _get_file_extension("http://example.com/image", None)


# Fixtures for download tests
@pytest.fixture
def mock_session():
    # Create a MagicMock instead of AsyncMock for the session
    session = MagicMock()
    response = AsyncMock()
    response.status = 200
    response.headers = {"Content-Type": "image/jpeg"}
    response.read.return_value = b"fake image data"

    # Create a proper async context manager
    context_manager = AsyncMock()
    context_manager.__aenter__.return_value = response

    # Make session.get return the context manager
    session.get.return_value = context_manager

    return session


@pytest.fixture
def mock_fsspec():
    with patch("fsspec.open") as mock_open:
        mock_file = MagicMock()
        mock_file.__enter__.return_value = mock_file
        mock_open.return_value = mock_file
        yield mock_open


# Tests for _download_single_resource
@pytest.mark.asyncio
async def test_download_single_resource_success(mock_session, mock_fsspec):
    url = "http://example.com/image.jpg"
    item_id = 123
    output_path = "/tmp/downloads"

    result = await _download_single_resource(mock_session, url, item_id, output_path)

    # Check that the session was used correctly
    mock_session.get.assert_called_once()

    # Check that the file was written correctly
    mock_fsspec.assert_called_once_with("/tmp/downloads/123.jpg", "wb")
    mock_fsspec().__enter__().write.assert_called_once_with(b"fake image data")

    # Check the return value
    assert result == "/tmp/downloads/123.jpg"


@pytest.mark.asyncio
async def test_download_single_resource_http_error(mock_session):
    # Override the status code to simulate an HTTP error
    mock_session.get.return_value.__aenter__.return_value.status = 404

    with pytest.raises(DownloadError) as excinfo:
        await _download_single_resource(
            mock_session, "http://example.com/image.jpg", 123, "/tmp/downloads"
        )

    assert "HTTP 404" in str(excinfo.value)


@pytest.mark.asyncio
async def test_download_single_resource_client_error(mock_session):
    # Make the get method raise an exception
    mock_session.get.side_effect = aiohttp.ClientError("Connection error")

    with pytest.raises(DownloadError) as excinfo:
        await _download_single_resource(
            mock_session, "http://example.com/image.jpg", 123, "/tmp/downloads"
        )

    assert "Connection error" in str(excinfo.value)


# Fixture for _download_resources_async tests
@pytest.fixture
def mock_download_single():
    with patch("images.downloader._download_single_resource") as mock:

        async def side_effect(_, url_arg, item_id, output_path):
            # Use _ for unused parameters
            return f"{output_path}/{item_id}.jpg"

        mock.side_effect = side_effect
        yield mock


# Tests for _download_resources_async
@pytest.mark.asyncio
async def test_download_resources_async_success(mock_download_single):
    urls = ["http://example.com/1.jpg", "http://example.com/2.jpg"]
    item_ids = [1, 2]
    output_path = "/tmp/downloads"
    max_parallel = 2

    results = await _download_resources_async(urls, item_ids, output_path, max_parallel)

    assert len(results) == 2
    assert results == ["/tmp/downloads/1.jpg", "/tmp/downloads/2.jpg"]
    assert mock_download_single.call_count == 2


@pytest.mark.asyncio
async def test_download_resources_async_error(mock_download_single):
    # Make the second download fail
    async def side_effect(_, _url, item_id, output_path):
        # Use _ for unused parameters
        if item_id == 1:
            return f"{output_path}/{item_id}.jpg"
        raise DownloadError("Failed to download")

    mock_download_single.side_effect = side_effect

    urls = ["http://example.com/1.jpg", "http://example.com/2.jpg"]
    item_ids = [1, 2]
    output_path = "/tmp/downloads"
    max_parallel = 2

    with pytest.raises(Exception):
        await _download_resources_async(urls, item_ids, output_path, max_parallel)


# Fixtures for download_resources tests
@pytest.fixture
def mock_async_download():
    with patch("images.downloader._download_resources_async") as mock:

        async def side_effect(*_, **__):
            # Use _ and __ to indicate unused parameters
            return ["/tmp/downloads/1.jpg", "/tmp/downloads/2.jpg"]

        mock.side_effect = side_effect
        yield mock


@pytest.fixture
def mock_makedirs():
    with patch("os.makedirs") as mock:
        yield mock


# Tests for download_resources
@pytest.mark.asyncio
@pytest.mark.parametrize(
    "output_path, should_create_dir",
    [
        ("/tmp/downloads", True),
        ("gs://my-bucket/downloads", False),
    ],
)
async def test_download_resources_path_handling(
    mock_async_download, mock_makedirs, output_path, should_create_dir
):
    urls = ["http://example.com/1.jpg", "http://example.com/2.jpg"]
    item_ids = [1, 2]

    result = await download_resources(urls, item_ids, output_path=output_path)

    # Check directory creation behavior based on path type
    if should_create_dir:
        mock_makedirs.assert_called_once_with(output_path, exist_ok=True)
    else:
        mock_makedirs.assert_not_called()

    # Check that the async download function was called
    mock_async_download.assert_called_once()

    # Check the result
    assert result == ["/tmp/downloads/1.jpg", "/tmp/downloads/2.jpg"]


@pytest.mark.asyncio
async def test_download_resources_different_length_inputs(mock_async_download):
    urls = ["http://example.com/1.jpg", "http://example.com/2.jpg"]
    item_ids = [1]

    with pytest.raises(ValueError) as excinfo:
        await download_resources(urls, item_ids)

    assert "same length" in str(excinfo.value)
    mock_async_download.assert_not_called()
