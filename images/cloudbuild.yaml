steps:
  # Build the container image with caching
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '--cache-from', 'gcr.io/$PROJECT_ID/image-downloader-job:latest',
      '-t', 'gcr.io/$PROJECT_ID/image-downloader-job',
      '-f', 'images/Dockerfile',
      '.'
    ]
    id: 'build'
  
  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/image-downloader-job']
    id: 'push'
  
  # Deploy Cloud Run Job (create if doesn't exist, update if it does)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        if gcloud run jobs describe image-downloader-job --region=us-central1 2>/dev/null; then
          echo "Updating existing Cloud Run job..."
          gcloud run jobs update image-downloader-job \
            --image gcr.io/$PROJECT_ID/image-downloader-job \
            --region us-central1 \
            --tasks 1 \
            --max-retries 1 \
            --task-timeout 8h \
            --memory 2048Mi \
            --command python \
            --args=-m,images.cli,gs://data-cardcomp/images,--max-parallel,100
        else
          echo "Creating new Cloud Run job..."
          gcloud run jobs create image-downloader-job \
            --image gcr.io/$PROJECT_ID/image-downloader-job \
            --region us-central1 \
            --tasks 1 \
            --max-retries 1 \
            --task-timeout 8h \
            --memory 2048Mi \
            --command python \
            --args=-m,images.cli,gs://data-cardcomp/images,--max-parallel,100
        fi
    id: 'deploy-job'
    
  # Deploy Cloud Scheduler (create if doesn't exist, update if it does)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        if gcloud scheduler jobs describe daily-image-download --location=us-central1 2>/dev/null; then
          echo "Updating existing Cloud Scheduler job..."
          gcloud scheduler jobs update http daily-image-download \
            --location=us-central1 \
            --schedule="0 0 * * *" \
            --uri="https://us-central1-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/$PROJECT_ID/jobs/image-downloader-job:run" \
            --http-method=POST \
            --oauth-service-account-email=$<EMAIL> \
            --oauth-token-scope=https://www.googleapis.com/auth/cloud-platform
        else
          echo "Creating new Cloud Scheduler job..."
          gcloud scheduler jobs create http daily-image-download \
            --location=us-central1 \
            --schedule="0 0 * * *" \
            --uri="https://us-central1-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/$PROJECT_ID/jobs/image-downloader-job:run" \
            --http-method=POST \
            --oauth-service-account-email=$<EMAIL> \
            --oauth-token-scope=https://www.googleapis.com/auth/cloud-platform
        fi
    id: 'deploy-scheduler'

images:
  - 'gcr.io/$PROJECT_ID/image-downloader-job'
