import random
import time
from datetime import datetime

import pandas as pd
import requests
from bs4 import BeautifulSoup
from tqdm.auto import tqdm

from listings.parser import Sport, is_valid_listing, parse_listing


def _fetch_basketball_card_sales(sport: Sport, page: int) -> requests.Response:
    """Fetch a page of basketball card sales from eBay."""
    assert sport in ["Basketball", "Football"]
    url = f"https://www.ebay.com/sch/i.html?_dcat=261328&_udlo=10&_fsrp=1&Sport={sport}&_sacat=214&LH_Complete=1&LH_Sold=1&_ipg=240&_pgn={page}&rt=nc"  # noqa: E501

    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",  # noqa: E501
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHT<PERSON>, like Gecko) Version/14.0 Safari/605.1.15",  # noqa: E501
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36",  # noqa: E501
    ]

    # Set up headers to mimic a browser
    headers = {
        "User-Agent": random.choice(user_agents),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Referer": "https://www.ebay.com/",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Cache-Control": "max-age=0",
    }

    # Make the request with a timeout
    response = requests.get(url, headers=headers, timeout=30)
    response.raise_for_status()
    return response


def scrape(
    num_pages: int = 5, delay_range: tuple[float, float] = (0.5, 2.0)
) -> pd.DataFrame:
    """
    Scrape eBay for basketball card listings.

    Args:
        num_pages: Number of pages to scrape
        delay_range: Range of random delay between requests (min, max)

    Returns:
        DataFrame containing the scraped listings
    """
    all_listings = []
    scraped_at = datetime.now()
    for sport_name in ["Basketball", "Football"]:
        # Use type assertion to tell pyright that these strings are valid Sport literals
        sport = sport_name  # type: ignore
        for i in tqdm(range(num_pages)):
            try:
                # Tell pyright that sport is a valid Sport type
                response = _fetch_basketball_card_sales(sport, i)  # type: ignore
                soup = BeautifulSoup(response.content, "html.parser")
                listings = soup.find_all("li", class_="s-item")

                # Skip the first listing on the first page as it's usually a header
                start_idx = 1 if i == 0 else 0

                for listing in listings[start_idx:]:
                    if is_valid_listing(listing):
                        try:
                            all_listings.append(
                                parse_listing(listing, sport, scraped_at)  # type: ignore
                            )
                        except Exception as e:
                            print(f"Error parsing listing: {e}")

                # Add a random delay between requests
                if i < num_pages - 1:
                    delay = random.uniform(*delay_range)
                    time.sleep(delay)

            except Exception as e:
                print(f"Error scraping page {i}: {e}")

    print(f"Total listings scraped: {len(all_listings)}")

    df = pd.DataFrame([listing.model_dump() for listing in all_listings])

    df = df.drop_duplicates("item_id")
    return df
