import re
from datetime import datetime
from decimal import Decimal
from typing import Any, Literal, cast

from pydantic import BaseModel

Sport = Literal["Basketball", "Football"]


class Listing(BaseModel):
    """Represents an eBay listing for a sports card."""

    title: str
    sport: str
    numeric_price: Decimal
    image_url: str
    item_id: str
    date_sold: datetime
    scraped_at: datetime
    shipping: Decimal | None
    epid: int | None = None


def _float_from_str(s: str) -> Decimal:
    """
    Extract a decimal value from a string.

    Args:
        s: The string to extract a decimal value from

    Returns:
        The extracted decimal value

    Raises:
        ValueError: If no decimal value is found in the string
    """
    s_search = re.search(r"(\d+\.\d+)", s.replace(",", ""))
    if not s_search:
        raise ValueError("No decimal value found in string")
    return Decimal(s_search.group(1))


def _get_epid(url: str) -> int:
    """Extract eBay product ID from URL if available."""
    epid_search = re.search(r"epid=(\d+)", url)
    if epid_search is None:
        return 0
    else:
        return int(epid_search.group(1))


def is_valid_listing(listing: Any) -> bool:  # type: ignore # noqa: ANN401
    """Check if a listing is valid (has been sold)."""
    return listing.find("span", class_="s-item__caption--signal POSITIVE") is not None


def parse_listing(
    listing: Any, sport: Sport, scraped_at: datetime  # noqa: ANN401
) -> Listing:  # type: ignore
    """Parse a listing element into a Listing object."""
    # Extract title
    title_elem = listing.find("div", class_="s-item__title")
    assert title_elem
    title = title_elem.get_text(strip=True)

    # Extract price
    price_elem = listing.find("span", class_="s-item__price")
    numeric_price = Decimal("0.0")
    if price_elem:
        price_text = price_elem.get_text(strip=True)
        numeric_price = _float_from_str(price_text)

    # Extract image URL
    img_elem = listing.find("img")
    assert img_elem
    image_url = cast(str, img_elem.get("data-defer-load") or img_elem.get("src", ""))

    # Extract item URL
    link_elem = listing.find("a", class_="s-item__link")
    assert link_elem

    url = cast(str, link_elem.get("href"))
    assert url

    # Extract item ID from URL
    item_id_search = re.search(r"itm/(\d+)", url)
    item_id = item_id_search.group(1) if item_id_search else ""

    # Extract date sold
    date_elem = listing.find("span", class_="s-item__caption--signal POSITIVE")
    assert date_elem
    date_sold_str = date_elem.get_text(strip=True).replace("Sold ", "").strip()
    date_sold = datetime.strptime(date_sold_str, "%b %d, %Y")

    # Extract shipping info
    shipping_elem = listing.find("span", class_="s-item__shipping") or listing.find(
        "span", class_="s-item__paidDeliveryInfo"
    )
    shipping = None
    if shipping_elem is not None:
        if shipping_elem.get_text(strip=True) == "Free delivery":
            shipping = Decimal("0.0")
        else:
            shipping = _float_from_str(shipping_elem.get_text(strip=True))

    # Get eBay product ID
    epid = _get_epid(url)

    return Listing(
        title=title,
        sport=sport,
        numeric_price=numeric_price,
        image_url=cast(str, image_url),
        item_id=item_id,
        date_sold=date_sold,
        scraped_at=scraped_at,
        shipping=shipping,
        epid=epid,
    )
