from datetime import datetime
from decimal import Decimal
from unittest.mock import MagicMock

import pytest
from bs4 import BeautifulSoup

from listings.parser import (
    Listing,
    _float_from_str,
    _get_epid,
    is_valid_listing,
    parse_listing,
)


# Tests for _float_from_str helper function
@pytest.mark.parametrize(
    "input_str, expected",
    [
        ("$123.45", Decimal("123.45")),
        ("Price: $99.99", Decimal("99.99")),
        ("$1,234.56", Decimal("1234.56")),
        ("Starting bid: $10.00", Decimal("10.00")),
    ],
)
def test_float_from_str(input_str, expected):
    """Test the _float_from_str function with various inputs."""
    result = _float_from_str(input_str)
    assert result == expected


def test_float_from_str_no_numbers():
    """Test that _float_from_str raises ValueError when no numbers are found."""
    with pytest.raises(ValueError, match="No decimal value found in string"):
        _float_from_str("No numbers here")

    with pytest.raises(ValueError, match="No decimal value found in string"):
        _float_from_str("")


# Tests for _get_epid helper function
@pytest.mark.parametrize(
    "url, expected",
    [
        (
            "https://www.ebay.com/itm/123456?epid=12345678",
            12345678,
        ),
        (
            "https://www.ebay.com/itm/123456?hash=item1&epid=87654321&other=param",
            87654321,
        ),
        ("https://www.ebay.com/itm/123456", 0),  # No epid should return 0
        ("https://www.ebay.com/itm/123456?epid=", 0),  # Empty epid should return 0
    ],
)
def test_get_epid(url, expected):
    """Test the _get_epid function with various URLs."""
    result = _get_epid(url)
    assert result == expected


# Test for is_valid_listing function
def test_is_valid_listing_true():
    """Test is_valid_listing returns True for a valid listing."""
    # Create a mock listing with a "Sold" span
    mock_listing = MagicMock()
    mock_listing.find.return_value = MagicMock()  # Non-None return value

    assert is_valid_listing(mock_listing) is True
    mock_listing.find.assert_called_once_with(
        "span", class_="s-item__caption--signal POSITIVE"
    )


def test_is_valid_listing_false():
    """Test is_valid_listing returns False for an invalid listing."""
    # Create a mock listing without a "Sold" span
    mock_listing = MagicMock()
    mock_listing.find.return_value = None

    assert is_valid_listing(mock_listing) is False
    mock_listing.find.assert_called_once_with(
        "span", class_="s-item__caption--signal POSITIVE"
    )


# Fixtures for parse_listing tests
@pytest.fixture
def mock_listing_html():
    """Create a mock HTML listing for testing."""
    html = """
    <li class="s-item">
        <div class="s-item__title">2023 Panini Prizm Cade Cunningham Silver Prizm PSA 10</div>
        <span class="s-item__price">$49.99</span>
        <img src="https://example.com/image.jpg"
             data-defer-load="https://example.com/image-large.jpg">
        <a class="s-item__link" href="https://www.ebay.com/itm/123456789?epid=12345678">Link</a>
        <span class="s-item__caption--signal POSITIVE">Sold Jun 15, 2023</span>
        <span class="s-item__shipping">$4.99 shipping</span>
    </li>
    """
    return BeautifulSoup(html, "html.parser")


@pytest.fixture
def mock_listing_html_free_shipping():
    """Create a mock HTML listing with free shipping for testing."""
    html = """
    <li class="s-item">
        <div class="s-item__title">2023 Panini Prizm Cade Cunningham Silver Prizm PSA 10</div>
        <span class="s-item__price">$49.99</span>
        <img src="https://example.com/image.jpg">
        <a class="s-item__link" href="https://www.ebay.com/itm/123456789">Link</a>
        <span class="s-item__caption--signal POSITIVE">Sold Jun 15, 2023</span>
        <span class="s-item__paidDeliveryInfo">Free delivery</span>
    </li>
    """
    return BeautifulSoup(html, "html.parser")


@pytest.fixture
def mock_listing_html_no_shipping():
    """Create a mock HTML listing without shipping info for testing."""
    html = """
    <li class="s-item">
        <div class="s-item__title">2023 Panini Prizm Cade Cunningham Silver Prizm PSA 10</div>
        <span class="s-item__price">$49.99</span>
        <img src="https://example.com/image.jpg">
        <a class="s-item__link" href="https://www.ebay.com/itm/123456789">Link</a>
        <span class="s-item__caption--signal POSITIVE">Sold Jun 15, 2023</span>
    </li>
    """
    return BeautifulSoup(html, "html.parser")


@pytest.fixture
def mock_listing_html_invalid_price():
    """Create a mock HTML listing with an invalid price format for testing."""
    html = """
    <li class="s-item">
        <div class="s-item__title">2023 Panini Prizm Cade Cunningham Silver Prizm PSA 10</div>
        <span class="s-item__price">Price not available</span>
        <img src="https://example.com/image.jpg">
        <a class="s-item__link" href="https://www.ebay.com/itm/123456789">Link</a>
        <span class="s-item__caption--signal POSITIVE">Sold Jun 15, 2023</span>
    </li>
    """
    return BeautifulSoup(html, "html.parser")


@pytest.fixture
def mock_listing_html_invalid_shipping():
    """Create a mock HTML listing with an invalid shipping format for testing."""
    html = """
    <li class="s-item">
        <div class="s-item__title">2023 Panini Prizm Cade Cunningham Silver Prizm PSA 10</div>
        <span class="s-item__price">$49.99</span>
        <img src="https://example.com/image.jpg">
        <a class="s-item__link" href="https://www.ebay.com/itm/123456789">Link</a>
        <span class="s-item__caption--signal POSITIVE">Sold Jun 15, 2023</span>
        <span class="s-item__shipping">Contact seller for shipping</span>
    </li>
    """
    return BeautifulSoup(html, "html.parser")


# Tests for parse_listing function
def test_parse_listing_basic(mock_listing_html):
    """Test basic parsing of a listing."""
    scraped_at = datetime(2023, 6, 20, 12, 0, 0)
    result = parse_listing(mock_listing_html, "Basketball", scraped_at)

    assert isinstance(result, Listing)
    assert result.title == "2023 Panini Prizm Cade Cunningham Silver Prizm PSA 10"
    assert result.sport == "Basketball"
    assert result.numeric_price == Decimal("49.99")
    assert result.image_url == "https://example.com/image-large.jpg"
    assert result.item_id == "123456789"
    assert result.date_sold == datetime(2023, 6, 15)
    assert result.scraped_at == scraped_at
    assert result.shipping == Decimal("4.99")
    assert result.epid == 12345678


def test_parse_listing_free_shipping(mock_listing_html_free_shipping):
    """Test parsing a listing with free shipping."""
    scraped_at = datetime(2023, 6, 20, 12, 0, 0)
    result = parse_listing(mock_listing_html_free_shipping, "Football", scraped_at)

    assert result.shipping == Decimal("0.0")
    assert result.sport == "Football"
    assert result.epid == 0  # No epid in the URL


def test_parse_listing_no_shipping(mock_listing_html_no_shipping):
    """Test parsing a listing without shipping information."""
    scraped_at = datetime(2023, 6, 20, 12, 0, 0)
    result = parse_listing(mock_listing_html_no_shipping, "Basketball", scraped_at)

    assert result.shipping is None


def test_parse_listing_invalid_price(mock_listing_html_invalid_price):
    """Test parsing a listing with an invalid price format."""
    scraped_at = datetime(2023, 6, 20, 12, 0, 0)
    with pytest.raises(ValueError, match="No decimal value found in string"):
        parse_listing(mock_listing_html_invalid_price, "Basketball", scraped_at)


def test_parse_listing_invalid_shipping(mock_listing_html_invalid_shipping):
    """Test parsing a listing with an invalid shipping format."""
    scraped_at = datetime(2023, 6, 20, 12, 0, 0)
    with pytest.raises(ValueError, match="No decimal value found in string"):
        parse_listing(mock_listing_html_invalid_shipping, "Basketball", scraped_at)


def test_parse_listing_missing_elements():
    """Test that parse_listing raises appropriate assertions for missing elements."""
    # Create a minimal HTML that's missing required elements
    html = "<li class='s-item'></li>"
    listing = BeautifulSoup(html, "html.parser")

    with pytest.raises(AssertionError):
        parse_listing(listing, "Basketball", datetime.now())
