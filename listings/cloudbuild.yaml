steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/ebay-scraper-job', '.']
  
  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/ebay-scraper-job']
  
  # Deploy Cloud Run Job (create if doesn't exist, update if it does)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        if gcloud run jobs describe ebay-scraper-job --region=us-central1 2>/dev/null; then
          echo "Updating existing Cloud Run job..."
          gcloud run jobs update ebay-scraper-job \
            --image gcr.io/$PROJECT_ID/ebay-scraper-job \
            --region us-central1 \
            --tasks 1 \
            --max-retries 3 \
            --task-timeout 1h \
            --memory 768Mi \
            --command python \
            --args ebay_scraper_cli.py,--pages,200
        else
          echo "Creating new Cloud Run job..."
          gcloud run jobs create ebay-scraper-job \
            --image gcr.io/$PROJECT_ID/ebay-scraper-job \
            --region us-central1 \
            --tasks 1 \
            --max-retries 3 \
            --task-timeout 1h \
            --memory 768Mi \
            --command python \
            --args ebay_scraper_cli.py,--pages,200
        fi
    id: 'deploy-job'
    
  # Deploy Cloud Scheduler (create if doesn't exist, update if it does)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        if gcloud scheduler jobs describe daily-ebay-scrape --location=us-central1 2>/dev/null; then
          echo "Updating existing Cloud Scheduler job..."
          gcloud scheduler jobs update http daily-ebay-scrape \
            --location=us-central1 \
            --schedule="0 */6 * * *" \
            --uri="https://us-central1-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/$PROJECT_ID/jobs/ebay-scraper-job:run" \
            --http-method=POST \
            --oauth-service-account-email=$<EMAIL> \
            --oauth-token-scope=https://www.googleapis.com/auth/cloud-platform
        else
          echo "Creating new Cloud Scheduler job..."
          gcloud scheduler jobs create http daily-ebay-scrape \
            --location=us-central1 \
            --schedule="0 */6 * * *" \
            --uri="https://us-central1-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/$PROJECT_ID/jobs/ebay-scraper-job:run" \
            --http-method=POST \
            --oauth-service-account-email=$<EMAIL> \
            --oauth-token-scope=https://www.googleapis.com/auth/cloud-platform
        fi
    id: 'deploy-scheduler'

images:
  - 'gcr.io/$PROJECT_ID/ebay-scraper-job'
