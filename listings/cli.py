#!/usr/bin/env python3
import os
from datetime import datetime
from urllib.parse import urlparse

import click

from listings.scraper import scrape


def generate_default_path() -> str:
    """Generate a default path with timestamp."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"gs://data-cardcomp/listings/ebay_listings_{timestamp}.parquet"


@click.command()
@click.option("--pages", default=5, help="Number of pages to scrape")
@click.option(
    "--output",
    "-o",
    required=True,
    help="Output path (gs://bucket/path.parquet or file://path.parquet or just path.parquet)",
)
@click.option("--min-delay", default=0.5, help="Minimum delay between requests")
@click.option("--max-delay", default=2.0, help="Maximum delay between requests")
@click.version_option(version="1.0.0")
def main(pages: int, output: str, min_delay: float, max_delay: float) -> int:
    """
    Scrape eBay listings and save to the specified path.

    Examples:
      - Save to GCS: --output gs://my-bucket/data.parquet
      - Save locally: --output file:///home/<USER>/data.parquet or just --output data.parquet
    """
    # Set default output path if none provided
    if output is None:
        output = generate_default_path()

    # If path doesn't have a protocol prefix, assume it's a local file
    if "://" not in output:
        output = f"file://{os.path.abspath(output)}"

    # Parse the URL to get protocol
    parsed_url = urlparse(output)
    protocol = parsed_url.scheme

    click.echo(f"Starting scrape of {pages} pages...")

    # Run the scrape function with progress bar
    with click.progressbar(length=pages, label="Scraping pages") as bar:
        df = scrape(num_pages=pages, delay_range=(min_delay, max_delay))
        bar.update(pages)

    click.echo(f"Scraped {len(df)} listings")

    # Set storage options based on protocol
    storage_options = {}
    if protocol == "gs":
        storage_options = {"token": None}  # Uses default credentials

    # For file protocol, strip the protocol for pandas if it's a local path
    save_path = output
    if protocol == "file":
        # Convert file:///path to /path for local files
        save_path = parsed_url.path

    df.to_parquet(
        save_path,
        engine="pyarrow",
        compression="snappy",
        storage_options=storage_options,
    )

    click.echo(f"Successfully saved to {output}")
    return 0


if __name__ == "__main__":
    main()
