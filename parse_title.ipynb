%load_ext autoreload
%autoreload 2

import pandas_gbq
from player_names import normalize_name, split_first_last
from player_names import extract_player_names_from_titles
from player_names import extract_player_names_llm



project_id = "cardcomp"
deals_df = pandas_gbq.read_gbq(
    "SELECT item_id, title FROM `cardcomp.ebay.scraped-ebay-view` LIMIT 10000",
    project_id=project_id,
)

# Remove NEW LISTING prefix
NEW_LISTING = "NEW LISTING"
has_new_listing_prefix = deals_df.title.str.upper().str.startswith(NEW_LISTING)
deals_df.loc[has_new_listing_prefix, "title"] = deals_df[has_new_listing_prefix].title.str[len(NEW_LISTING):]


players_df = pandas_gbq.read_gbq("SELECT * FROM `cardcomp.ebay.players`")

# Precalculate normalized first and last names
players_df = players_df.drop_duplicates("name")
fl = players_df.name.map(split_first_last).map(lambda fl: (normalize_name(fl[0]), normalize_name(fl[1])))
players_df["first_name"] = fl.map(lambda fl: fl[0])
players_df["last_name"] = fl.map(lambda fl: fl[1])

# Bootstrap aliases with last/first names
last_first = players_df.name.map(lambda full_name: " ".join(split_first_last(full_name)[::-1]))
aliases = dict(zip(last_first.tolist(), players_df.name.tolist()))

deals_df["full_name"] = extract_player_names_from_titles(deals_df.title.tolist(), list(players_df.name), aliases)
deals_df["full_name_raw"] = deals_df["full_name"]

# Precalculate normalized first and last names
fl = deals_df[deals_df.full_name.notna()].full_name.map(split_first_last).map(lambda fl: (normalize_name(fl[0]), normalize_name(fl[1])))
deals_df["first_name"] = fl.map(lambda fl: fl[0])
deals_df["last_name"] = fl.map(lambda fl: fl[1])

deals_df.full_name.isna().sum()

import os

api_key = os.environ["GOOGLE_API_KEY"]
api_key = "AIzaSyCnbwz_kUdvXMScKBS-Izqs_EsUFc0vSBQ"
# player_names_llm = extract_player_names_llm(deals_df[deals_df.full_name.isna()][:30].title.tolist(), api_key)


player_names_llm

# def find_aliases(player_names_llm, players_df) -> dict[str, str]:
aliases = {}

for player_llm in player_names_llm:
    first_llm = normalize_name(split_first_last(player_llm)[0])
    last_llm = normalize_name(split_first_last(player_llm)[1])

    candidates = players_df[(players_df.first_name == first_llm) & (players_df.last_name == last_llm)]
    # print(player_llm, candidates.shape)
    if len(candidates) == 1:
        aliases[player_llm] = candidates.name.values[0]
    else:
        aliases[player_llm] = player_llm
aliases

deals_df["full_name"] = extract_player_names_from_titles(deals_df.title.tolist(), list(players_df.name), aliases)

deals_df[deals_df.full_name_raw.isna() & deals_df.full_name.notna()]

