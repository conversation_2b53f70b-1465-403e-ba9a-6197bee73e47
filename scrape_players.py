import random
import string
import time
from typing import cast

import pandas as pd
import requests
from bs4 import BeautifulSoup, Tag
from tqdm.auto import tqdm

# Constants
REQUEST_TIMEOUT = 15
SHORT_DELAY = 1
LONG_DELAY = 1
MAX_LINE_LENGTH = 100


def randomize_header() -> dict[str, str]:
    """Generate randomized HTTP headers to mimic browser requests."""

    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",  # noqa: E501
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Safari/605.1.15",  # noqa: E501
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36",  # noqa: E501
    ]

    # Set up headers to mimic a browser
    headers = {
        "User-Agent": random.choice(user_agents),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Referer": "https://www.ebay.com/",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Cache-Control": "max-age=0",
    }
    return headers


def scrape_nfl_players() -> list[str]:
    """Scrape NFL player names from Pro Football Reference, handling pagination."""
    base_url = "https://www.pro-football-reference.com"
    names = []

    # Loop through each letter of the alphabet (A-Z)
    for letter in tqdm(string.ascii_uppercase, desc="Scraping NFL players"):
        current_page_url = f"{base_url}/players/{letter}/"

        # Loop for pagination within each letter
        while current_page_url:
            try:
                # Make the HTTP request
                response = requests.get(
                    current_page_url,
                    headers=randomize_header(),
                    timeout=REQUEST_TIMEOUT,
                )
                response.raise_for_status()

                # Parse the HTML content
                soup = BeautifulSoup(response.text, "html.parser")

                # Find the main div containing player lists
                players_div = soup.find("div", id="div_players")
                if not players_div:
                    warning_msg = (
                        f"  Warning: Could not find 'div_players' for "
                        f"{current_page_url}. Moving to next letter."
                    )
                    print(warning_msg)
                    break  # No players div, move to next letter or break pagination

                # Find all player names within <p> tags inside the players_div
                players_div_tag = cast(Tag, players_div)
                player_paragraphs = players_div_tag.find_all("p")
                for p_tag in player_paragraphs:
                    if isinstance(p_tag, Tag):
                        link = p_tag.find("a")  # Get the <a> tag within the <p>
                        if isinstance(link, Tag):
                            full_name = link.text.strip()
                            if full_name:  # Ensure the name isn't empty
                                names.append(full_name)

                # Check for a "Next page of players" link for pagination
                next_page_link = soup.find("a", text="Next page of players")

                if next_page_link and isinstance(next_page_link, Tag):
                    href = next_page_link.get("href")
                    if href:
                        current_page_url = f"{base_url}{href}"
                        time.sleep(LONG_DELAY)  # Be polite: longer delay between pages
                    else:
                        current_page_url = None
                else:
                    current_page_url = None  # No more pages for this letter

            except requests.exceptions.HTTPError as errh:
                print(f"  HTTP Error for {current_page_url}: {errh}")
                current_page_url = None  # Stop for this letter
            except requests.exceptions.ConnectionError as errc:
                print(f"  Error Connecting for {current_page_url}: {errc}")
                current_page_url = None  # Stop for this letter
            except requests.exceptions.Timeout as errt:
                print(f"  Timeout Error for {current_page_url}: {errt}")
                current_page_url = None  # Stop for this letter
            except requests.exceptions.RequestException as err:
                print(
                    f"  An unexpected request error occurred for {current_page_url}: {err}"
                )
                current_page_url = None  # Stop for this letter
            except Exception as e:
                print(f"  An error occurred during parsing for {current_page_url}: {e}")
                current_page_url = None  # Stop for this letter

            time.sleep(SHORT_DELAY)  # Be polite: short delay between requests

    return names


def scrape_nba_players() -> list[str]:
    """Scrape NBA player names from Basketball Reference."""
    base_url = "https://www.basketball-reference.com/players/"
    names = []

    # Iterate through each letter of the alphabet
    for letter in tqdm(string.ascii_lowercase, desc="Scraping NBA players"):
        url = f"{base_url}{letter}/"

        try:
            response = requests.get(url, headers=randomize_header())
            response.raise_for_status()

            soup = BeautifulSoup(response.content, "html.parser")

            # Find the table containing player names
            # Players are typically in a table with id="players"
            players_table = soup.find("table", {"id": "players"})

            if players_table:
                # Find all player name links in the table
                players_table_tag = cast(Tag, players_table)
                player_links = players_table_tag.find_all("a", href=True)

                for link in player_links:
                    # Check if this is a player link (contains '/players/' in href)
                    if isinstance(link, Tag):
                        href = link.get("href", "")
                        if isinstance(href, str) and "/players/" in href:
                            full_name = link.text.strip()

                            # Skip if name is empty or contains non-alphabetic characters
                            # (like asterisks for Hall of Fame)
                            cleaned_name = "".join(
                                c for c in full_name if c.isalpha() or c.isspace()
                            ).strip()
                            names.append(cleaned_name)

            # Be respectful to the server
            time.sleep(SHORT_DELAY)

        except requests.RequestException as e:
            print(f"Error scraping letter '{letter}': {e}")
            continue
        except Exception as e:
            print(f"Unexpected error for letter '{letter}': {e}")
            continue

    print(f"Successfully scraped {len(names)} player names")
    return names


def scrape_wnba_players() -> list[str]:
    """
    Scrapes WNBA player names from basketball-reference.com/wnba/

    Returns:
        List of full player names as strings
    """
    base_url = "https://www.basketball-reference.com/wnba/players/"
    full_names = []

    session = requests.Session()

    # Iterate through each letter of the alphabet
    for letter in tqdm(string.ascii_lowercase, desc="Scraping WNBA players"):
        url = f"{base_url}{letter}"

        response = session.get(url, headers=randomize_header())
        response.raise_for_status()

        soup = BeautifulSoup(response.content, "html.parser")

        # Find the div containing player names
        # WNBA players are typically in a div with id="content"
        all_alphabet = soup.find("div", {"id": "content"})

        if all_alphabet:
            # Find all player name links in the div
            all_alphabet_tag = cast(Tag, all_alphabet)
            player_links = all_alphabet_tag.select("p a")

            for link in player_links:
                # Check if this is a player link (contains '/wnba/players/' in href)
                if isinstance(link, Tag):
                    href = link.get("href", "")
                    if isinstance(href, str) and "/wnba/players/" in href:
                        full_name = link.text.strip()

                        # Clean the name - remove special characters like asterisks
                        cleaned_name = "".join(
                            c for c in full_name if c.isalpha() or c.isspace()
                        ).strip()

                        if cleaned_name:
                            full_names.append(cleaned_name)

        # Be respectful to the server
        time.sleep(10)

    return full_names


def main() -> None:
    """Main function to scrape all player names and save to files."""
    basketball_names = scrape_wnba_players()
    basketball_names += scrape_nba_players()
    football_names = scrape_nfl_players()

    bdf = pd.DataFrame({"name": basketball_names})
    bdf["sport"] = "Basketball"

    fdf = pd.DataFrame({"name": football_names})
    fdf["sport"] = "Football"

    all_players = pd.concat([bdf, fdf])
    all_players.to_parquet("gs://data-cardcomp/players.parquet", index=False)
    all_players.to_csv("gs://data-cardcomp/players.csv", index=False)


main()
