#!/usr/bin/env python3
"""
Comprehensive pytest tests for extract_player_names_from_titles function.
"""

import pytest
from unittest.mock import patch
import pandas as pd

from player_names import extract_player_names_from_titles


@pytest.fixture
def known_names():
    """Test data for known player names."""
    return [
        "<PERSON>",
        "<PERSON><PERSON><PERSON> James",
        "<PERSON> Bryant",
        "<PERSON> Johnson",
        "<PERSON> Bird",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON>"
    ]


@pytest.fixture
def aliases():
    """Test data for player aliases."""
    return {
        "MJ": "<PERSON>",
        "<PERSON> James": "<PERSON><PERSON><PERSON> James",
        "Black Mamba": "<PERSON> Bryant",
        "<PERSON>haq": "<PERSON><PERSON><PERSON><PERSON>",
        "The Big Fundamental": "<PERSON>"
    }

def test_basic_functionality(known_names, aliases):
    """Test basic player name extraction."""
    titles = [
        "Michael Jordan 1991 Upper Deck Basketball Card",
        "LeBron James Rookie Card PSA 10",
        "Vintage Baseball Card Collection",
        "Kobe Bryant Lakers Jersey Card"
    ]

    expected = [
        "<PERSON>",
        "<PERSON><PERSON><PERSON> James",
        None,
        "<PERSON>"
    ]

    result = extract_player_names_from_titles(titles, known_names, aliases)
    assert result == expected


def test_length_consistency(known_names, aliases):
    """Test that output length matches input length."""
    titles = ["Card 1", "Card 2", "Card 3", "<PERSON> Card"]
    result = extract_player_names_from_titles(titles, known_names, aliases)
    assert len(result) == len(titles)


def test_name_swapping(known_names, aliases):
    """Test that swapped names (Last, First) are detected."""
    titles = [
        "Jordan Michael Autograph Card",
        "James LeBron Rookie Card",
        "<PERSON> Kobe Championship Card"
    ]

    expected = [
        "Michael Jordan",
        "LeBron James",
        "Kobe <PERSON>"
    ]

    result = extract_player_names_from_titles(titles, known_names, aliases)
    assert result == expected

def test_aliases(known_names, aliases):
    """Test that aliases are properly matched."""
    titles = [
        "MJ 1991 Championship Card",
        "King James Cleveland Card",
        "Black Mamba Retirement Card",
        "Shaq Lakers Dominance Card",
        "The Big Fundamental Spurs Card"
    ]

    expected = [
        "Michael Jordan",
        "LeBron James",
        "Kobe Bryant",
        "Shaquille O'Neal",
        "Tim Duncan"
    ]

    result = extract_player_names_from_titles(titles, known_names, aliases)
    assert result == expected


def test_none_and_empty_inputs(known_names, aliases):
    """Test handling of None and empty string inputs."""
    titles = [
        None,
        "",
        "   ",  # whitespace only
        "Michael Jordan Card",
        None
    ]

    expected = [
        None,
        None,
        None,
        "Michael Jordan",
        None
    ]

    result = extract_player_names_from_titles(titles, known_names, aliases)
    assert result == expected


def test_no_matches(known_names, aliases):
    """Test titles with no player names."""
    titles = [
        "Vintage Baseball Card Collection",
        "Random Sports Memorabilia",
        "Basketball Trading Cards",
        "Sports Equipment Sale"
    ]

    expected = [None, None, None, None]

    result = extract_player_names_from_titles(titles, known_names, aliases)
    assert result == expected

def test_multiple_players_first_match(known_names, aliases):
    """Test that when multiple players are in a title, first match is returned."""
    titles = [
        "Michael Jordan vs Magic Johnson 1991 Finals Card",
        "Larry Bird and Magic Johnson Rivalry Card"
    ]

    # Should return the first player found
    result = extract_player_names_from_titles(titles, known_names, aliases)

    # Verify we get valid player names (exact order may depend on processing)
    assert result[0] is not None
    assert result[0] in known_names
    assert result[1] is not None
    assert result[1] in known_names


def test_case_insensitive_matching(known_names, aliases):
    """Test that matching is case insensitive."""
    titles = [
        "michael jordan card",
        "LEBRON JAMES ROOKIE",
        "kObE bRyAnT jersey"
    ]

    expected = [
        "Michael Jordan",
        "LeBron James",
        "Kobe Bryant"
    ]

    result = extract_player_names_from_titles(titles, known_names, aliases)
    assert result == expected


def test_special_characters_and_accents(known_names, aliases):
    """Test handling of special characters and accents."""
    titles = [
        "Shaquille O'Neal Rookie Card",
        "Michael Jordan Championship Ring Card"  # Without possessive
    ]

    expected = [
        "Shaquille O'Neal",
        "Michael Jordan"
    ]

    result = extract_player_names_from_titles(titles, known_names, aliases)
    assert result == expected

def test_possessive_forms(known_names, aliases):
    """Test that possessive forms don't match (by design)."""
    titles = [
        "Michael Jordan's Championship Ring Card",
        "Kobe Bryant's Final Game Card"
    ]

    # Possessive forms should not match due to apostrophe removal in normalize()
    expected = [None, None]

    result = extract_player_names_from_titles(titles, known_names, aliases)
    assert result == expected


def test_empty_known_names(aliases):
    """Test behavior with empty known names list."""
    titles = ["Michael Jordan Card", "LeBron James Card"]
    result = extract_player_names_from_titles(titles, [], aliases)
    expected = [None, None]
    assert result == expected


def test_empty_aliases(known_names):
    """Test behavior with empty aliases dictionary."""
    titles = ["Michael Jordan Card", "MJ Card"]
    result = extract_player_names_from_titles(titles, known_names, {})
    expected = ["Michael Jordan", None]  # MJ won't match without aliases
    assert result == expected


def test_priority_known_names_over_aliases(known_names, aliases):
    """Test that known names take priority over aliases."""
    # Add a name that could also be an alias
    titles = ["Magic Johnson Card"]

    # Magic Johnson is in known_names, so should return that even if there's an alias
    result = extract_player_names_from_titles(titles, known_names, aliases)
    expected = ["Magic Johnson"]
    assert result == expected


@patch('player_names.tqdm')
def test_progress_bar_called(mock_tqdm, known_names, aliases):
    """Test that progress bar is used."""
    titles = ["Michael Jordan Card"]
    mock_tqdm.return_value = titles  # Mock tqdm to return the titles

    extract_player_names_from_titles(titles, known_names, aliases)

    # Verify tqdm was called with correct parameters
    mock_tqdm.assert_called_once_with(titles, desc="Extracting player names")


if __name__ == "__main__":
    # Run the tests with pytest
    pytest.main([__file__, "-v"])
